#!/usr/bin/env python3
import argparse, shutil, sys
from pathlib import Path
import numpy as np
from sklearn.cluster import MeanShift, estimate_bandwidth
from data_utils import load_nii, save_like, discover_sample_dirs, robust_norm

SEED = 612385

def fit_meanshift_fixed_k(x: np.ndarray, target_k: int,
                          init_bw: float | None, quantile: float, sample_size: int,
                          max_iter: int = 10):
    x = x.reshape(-1, 1).astype(np.float32)

    if init_bw is None:
        bw = estimate_bandwidth(
            x,
            quantile=quantile,
            n_samples=min(sample_size, x.shape[0]),
            random_state=SEED,
        )
    else:
        bw = init_bw

    low, high = bw * 0.1, bw * 10.0
    best_labels, best_centers = None, None
    best_diff = float("inf")

    for _ in range(max_iter):
        ms = MeanShift(bandwidth=bw, bin_seeding=True)
        ms.fit(x)
        labels, centers = ms.labels_, ms.cluster_centers_.reshape(-1)
        n_clusters = len(np.unique(labels))

        diff = abs(n_clusters - target_k)
        if diff < best_diff:
            best_labels, best_centers = labels, centers
            best_diff = diff
            if diff == 0:
                break

        if n_clusters > target_k:
            bw *= 1.2
        else:
            bw *= 0.8
        bw = min(max(bw, low), high)

    return best_labels, best_centers

def relabel_by_center(labels: np.ndarray, centers: np.ndarray, base: int) -> np.ndarray:
    uniq = np.unique(labels)
    order = np.argsort([centers[k] for k in uniq])
    mapping = {int(uniq[i]): base + 1 + i for i in order}
    return np.vectorize(lambda t: mapping[int(t)])(labels)

def two_phase_ms_labels(image: np.ndarray, mask_bool: np.ndarray,
                        bandwidth: float | None, quantile: float, sample_size: int,
                        brain_clusters: int, nonbrain_clusters: int):
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat = mask_bool.reshape(-1)
    z = robust_norm(img_flat)
    labels_flat = np.zeros_like(img_flat, dtype=np.int32)

    lab_b, ctr_b = fit_meanshift_fixed_k(z[m_flat], brain_clusters,
                                         bandwidth, quantile, sample_size)
    lab_b = relabel_by_center(lab_b, ctr_b, base=0)
    labels_flat[m_flat] = lab_b
    n_brain = brain_clusters

    lab_nb, ctr_nb = fit_meanshift_fixed_k(z[~m_flat], nonbrain_clusters,
                                           bandwidth, quantile, sample_size)
    lab_nb = relabel_by_center(lab_nb, ctr_nb, base=n_brain)
    labels_flat[~m_flat] = lab_nb
    n_bg = nonbrain_clusters

    return labels_flat.reshape(shp), n_brain, n_bg

def process_case(case_dir: Path, dst_root: Path,
                 bandwidth: float | None, quantile: float,
                 sample_size: int, brain_clusters: int,
                 nonbrain_clusters: int, copy_inputs: bool):
    image, aff, hdr = load_nii(case_dir / "image.nii.gz")
    mask, _, _ = load_nii(case_dir / "mask.nii.gz")
    mask_bool = mask > 0

    labels, n_brain, n_bg = two_phase_ms_labels(
        image, mask_bool, bandwidth, quantile, sample_size,
        brain_clusters, nonbrain_clusters
    )

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)
    save_like(labels, aff, hdr, dst_case / "labels.nii.gz", dtype=np.uint16)

    if copy_inputs:
        shutil.copy2(case_dir / "image.nii.gz", dst_case / "image.nii.gz")
        shutil.copy2(case_dir / "mask.nii.gz", dst_case / "mask.nii.gz")

    return True, n_brain, n_bg

def write_global_mapping(dst_root: Path, brain_clusters: int, nonbrain_clusters: int):
    mapping_file = dst_root / "mapping_ms_global.csv"
    mapping_file.parent.mkdir(parents=True, exist_ok=True)

    with open(mapping_file, "w") as f:
        f.write("label,mapping,class_name\n")  # header

        # brain clusters → mapping=1
        for i in range(1, brain_clusters + 1):
            f.write(f"{i},1,brain_cluster_{i}\n")

        # non-brain clusters → mapping=0
        for j in range(1, nonbrain_clusters + 1):
            f.write(f"{brain_clusters + j},0,nonbrain_cluster_{j}\n")

    print(f"Global mapping written to {mapping_file}")


def main():
    ap = argparse.ArgumentParser(description="Two-phase Mean Shift with adaptive bandwidth to target clusters")
    ap.add_argument("--src", required=True, type=Path)
    ap.add_argument("--dst", required=True, type=Path)
    ap.add_argument("--prefix", default=None)
    ap.add_argument("--copy_inputs", action="store_true")
    ap.add_argument("--ms_bandwidth", type=float, default=None)
    ap.add_argument("--ms_quantile", type=float, default=0.20)
    ap.add_argument("--ms_sample", type=int, default=5000)
    ap.add_argument("--brain_clusters", type=int, required=True)
    ap.add_argument("--nonbrain_clusters", type=int, required=True)
    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)
    n_ok = 0
    for case in cases:
        ok, nb, ng = process_case(
            case, args.dst,
            args.ms_bandwidth, args.ms_quantile, args.ms_sample,
            args.brain_clusters, args.nonbrain_clusters, args.copy_inputs
        )
        n_ok += int(ok)
        print(f"[{case.name}] clusters: brain={nb}, nonbrain={ng}")

    write_global_mapping(args.dst, args.brain_clusters, args.nonbrain_clusters)
    print(f"\nProcessed {n_ok}/{len(cases)} cases.")

if __name__ == "__main__":
    main()
