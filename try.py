#!/usr/bin/env python3
"""
One-phase GMM segmentation on entire image (ignores brain/background masks).
- Fits a single GaussianMixture over the whole intensity distribution.
- Saves labels.nii.gz in each case folder under --dst.
"""

import argparse, sys, shutil
from pathlib import Path
import numpy as np
from sklearn.mixture import GaussianMixture

from data_utils import (
    load_nii, save_like, discover_sample_dirs, robust_norm, write_global_mapping_csv
)

IMAGE_FILENAME = "image.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

# GMM hyperparams
COV  = "full"      # covariance_type
REG  = 1e-6        # reg_covar
SEED = 612385      # random_state

# ------------------- GMM helper -------------------

def fit_gmm(x: np.ndarray, K: int):
    """Fit a 1D GMM on flattened intensities (normalized)."""
    x = x.reshape(-1, 1)
    n = x.shape[0]
    if n == 0:
        return None
    if float(np.var(x)) < 1e-10:
        return None
    k_eff = min(K, n)
    if k_eff < 1:
        return None

    gm = GaussianMixture(
        n_components=k_eff,
        covariance_type=COV,
        reg_covar=REG,
        random_state=SEED,
        init_params="kmeans",
        max_iter=300,
        tol=1e-4,
    )
    gm.fit(x)
    return gm

def whole_image_gmm_labels(image: np.ndarray, K: int):
    """Cluster all voxels in image into K classes via GMM."""
    shp = image.shape
    flat = image.reshape(-1).astype(np.float32)

    # normalize intensities
    flat_norm = robust_norm(flat)

    gm = fit_gmm(flat_norm, K)
    if gm:
        labels = gm.predict(flat_norm.reshape(-1,1)) + 1  # labels 1..K
    else:
        labels = np.ones_like(flat, dtype=np.int32)

    return labels.reshape(shp)

def process_case(case_dir: Path, dst_root: Path, K: int, copy_inputs: bool):
    img_p = case_dir / IMAGE_FILENAME
    if not img_p.exists():
        print(f"[skip] missing image in: {case_dir}", file=sys.stderr)
        return False

    image, aff, hdr = load_nii(img_p)
    labels = whole_image_gmm_labels(image=image, K=K)

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    save_like(labels, aff, hdr, dst_case / TRUTH_FILENAME, dtype=np.uint16)

    if copy_inputs:
        shutil.copy2(img_p, dst_case / IMAGE_FILENAME)

    return True

def main():
    ap = argparse.ArgumentParser(description="One-phase GMM segmentation on whole image.")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--prefix", default=None, help="Only process folders containing this string")
    ap.add_argument("--K", type=int, default=5, help="Number of GMM components (clusters)")
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image into --dst case folders")
    ap.add_argument("--mapping_csv", type=Path, default=None, help="Defaults to <dst>/config/mapping.csv")
    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    for case in cases:
        n_ok += int(process_case(case, args.dst, args.K, args.copy_inputs))

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    mapping_csv = args.mapping_csv or (args.dst / "config" / "mapping.csv")
    write_global_mapping_csv(args.K, 0, mapping_csv)

    print(f"Processed {n_ok}/{len(cases)} cases.")
    print(f"Mapping written to: {mapping_csv}")

if __name__ == "__main__":
    main()
